<!DOCTYPE html>
<html lang="en">
<head>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-T4N8L0SRWZ"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-T4N8L0SRWZ');
  </script>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title id="page-title">SnowNavi Snow Club</title>
  <meta name="description" content="SnowNavi Snow Club offers all levels of snowboard and ski lessons and a interactive ski map designed for ski route planning. Join our courses and explore the slopes with confidence." />
  <meta name="keywords" content="ski lessons, snowboard lessons, interactive ski map, ski route planning, SnowNavi, ski club, BASI, instructor course, ski in Europe,滑雪俱乐部,滑雪课程,滑雪地图,滑雪自由式,中文滑雪课, Nederlands skiles" />
  <meta name="author" content="SnowNavi" />
  <meta property="og:title" content="SnowNavi Snow Club" />
  <meta property="og:description" content="All levels of ski and snowboard lessons and an interactive ski route planning map in English, 中文, and Nederlands. Since 2021." />
  <meta property="og:type" content="website" />
  <meta property="og:image" content="assets/picture/snownavi_logo_banner.jpg" />
  <meta property="og:url" content="https://snownavi.ski" />
  <link rel="canonical" href="https://snownavi.ski" />
  <style>
    :root {
      --main-red: #E53512;
      --bg-light: #F9F4F3;
      --text-dark: #2F2F2F;
      --text-gray: #717171;
      --contrast-white: #FFFFFF;
      --accent-blue: #9ED4E7;
    }
    body {
      margin: 0;
      font-family: 'Noto Sans SC', sans-serif;
      background-color: var(--bg-light);
      color: var(--text-dark);
    }
    header {
      background: var(--contrast-white);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem 2rem;
      box-shadow: 0 2px 6px rgba(0,0,0,0.05);
      position: relative;
    }
    header img {
      height: 40px;
    }
    .menu-toggle {
      display: none;
      font-size: 1.5rem;
      background: none;
      border: none;
      cursor: pointer;
      color: var(--text-dark);
    }
    nav.nav-links {
      display: flex;
      align-items: center;
    }
    nav.nav-links a {
      margin-left: 1.5rem;
      text-decoration: none;
      color: var(--text-dark);
      font-weight: bold;
    }
    .language-selector {
      margin-left: 2rem;
      font-size: 1rem;
    }

    @media (max-width: 768px) {
      .menu-toggle {
        display: block;
      }
      nav.nav-links {
        display: none;
        flex-direction: column;
        background: var(--contrast-white);
        position: absolute;
        top: 100%;
        right: 0;
        width: 220px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        z-index: 1000;
      }
      nav.nav-links.open {
        display: flex;
      }
      nav.nav-links a,
      .language-selector {
        margin: 1rem;
      }
    }

    nav {
      display: flex;
      align-items: center;
    }
    nav a {
      margin-left: 1.5rem;
      text-decoration: none;
      color: var(--text-dark);
      font-weight: bold;
    }
    .language-selector {
      margin-left: 2rem;
      font-size: 1rem;
    }
    .hero {
      background: url('assets/picture/hero.jpg') center/cover;
      color: white;
      padding: 6rem 2rem;
      text-align: center;
    }
    .hero h1 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
    }
    .hero .cta-btn {
      background: var(--main-red);
      color: white;
      padding: 0.8rem 1.5rem;
      border: none;
      border-radius: 8px;
      font-size: 1rem;
      cursor: pointer;
      text-decoration: none;
      display: inline-block;
    }
    .section {
      padding: 3rem 2rem;
      max-width: 1200px;
      margin: auto;
    }
    .courses {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
    }
    .course-card {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 8px rgba(0,0,0,0.05);
    }
    .course-card img {
      width: 100%;
      height: 180px;
      object-fit: cover;
    }
    .course-card h3 {
      margin: 1rem;
    }
    .course-card a {
      margin: 0 1rem 1rem;
      display: inline-block;
      background: var(--main-red);
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      cursor: pointer;
      text-decoration: none;
    }
    .map-section, .story-section {
      text-align: center;
      margin: 4rem auto;
      padding: 0 2rem;
    }
    .map-section h2, .story-section h2 {
      margin-bottom: 1rem;
    }
    .map-section img {
      max-width: 100%;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      cursor: pointer;
    }
    .timeline {
      max-width: 800px;
      margin: 2rem auto;
      text-align: left;
    }
    .timeline-item {
      display: flex;
      margin-bottom: 2rem;
      align-items: flex-start;
    }
    .timeline-year {
      background: var(--main-red);
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      font-weight: bold;
      min-width: 80px;
      text-align: center;
      margin-right: 1.5rem;
      flex-shrink: 0;
    }
    .timeline-content {
      flex: 1;
    }
    .timeline-content h3 {
      margin: 0 0 0.5rem 0;
      color: var(--text-dark);
      font-size: 1.2rem;
    }
    .timeline-location {
      color: var(--text-gray);
      font-size: 0.9rem;
      margin: 0 0 0.5rem 0;
      font-style: italic;
    }
    .timeline-content p {
      margin: 0 0 0.5rem 0;
      line-height: 1.6;
    }
    .timeline-future {
      font-style: italic;
      color: var(--main-red);
      font-weight: bold;
    }
    @media (max-width: 768px) {
      .timeline-item {
        flex-direction: column;
      }
      .timeline-year {
        margin-right: 0;
        margin-bottom: 0.5rem;
        align-self: flex-start;
      }
    }
    footer {
      background: var(--text-dark);
      color: white;
      padding: 2rem;
      text-align: center;
    }
    #toTop {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      background: var(--main-red);
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      cursor: pointer;
      display: none;
      z-index: 1000;
      font-size: 0.9rem;
    }
  </style>
</head>
<body>
  <header>
    <img src="assets/picture/snownavi_logo_banner.jpg" alt="SnowNavi logo">
    <button class="menu-toggle" id="menu-toggle">☰</button>
    <nav class="nav-links" id="nav-links">
      <!-- Navigation items will be dynamically inserted here -->
      <div class="language-selector">
        <select id="lang" class="language-selector">
          <option value="en">🇬🇧 EN</option>
          <option value="zh">🇨🇳 中文</option>
          <option value="nl">🇳🇱 NL</option>
        </select>
      </div>
    </nav>
  </header>

  <section class="hero" id="hero">
    <h1>Whether you're leveling up or starting fresh, we ride with you</h1>
    <a href="#courses" class="cta-btn">Join SnowNavi</a>
  </section>

  <section id="courses" class="section">
    <h2>Our Courses</h2>
    <div class="courses">
      <div class="course-card">
        <img src="assets/picture/basi.jpg" alt="BASI">
        <h3>BASI Instructor Course</h3>
        <a href="course.html?course=basi">Learn More</a>
      </div>
      <div class="course-card">
        <img src="assets/picture/park.jpg" alt="Park and Freestyle">
        <h3>Park Freestyle Lessons</h3>
        <a href="course.html?course=park">Learn More</a>
      </div>
      <div class="course-card">
        <img src="assets/picture/private.jpg" alt="Private">
        <h3>Private Lessons</h3>
        <a href="course.html?course=private">Learn More</a>
      </div>
      <div class="course-card">
        <img src="assets/picture/group.jpg" alt="Group">
        <h3>Group Lessons</h3>
        <a href="course.html?course=group">Learn More</a>
      </div>
      <div class="course-card">
        <img src="assets/picture/riding_week.jpg" alt="Riding Week">
        <h3>Riding Week</h3>
        <a href="course.html?course=riding_week">Learn More</a>
      </div>
    </div>
  </section>

  <section id="map" class="map-section">
    <h2>Interactive Ski Map</h2>
    <a href="https://snownavi.ski/preview" target="_blank">
      <img src="assets/picture/skimap.png" alt="Interactive Ski Map">
    </a>
  </section>

  <section id="story" class="story-section">
    <h2>🏂 SnowNavi的故事｜从雪友到雪匠</h2>
    <p>“Snow” stands for purity and passion, while “Navi” means guidance. We are committed to leading our partners through a fun ride on snow. — Since 2021 @ 🇳🇱</p>
    <div class="timeline">
      <div class="timeline-item">
        <div class="timeline-year">2017</div>
        <div class="timeline-content">
          <h3>起源：雪季的约定</h3>
          <p class="timeline-location">📍 荷兰·埃因霍温</p>
          <p>一群热爱冒险的朋友在圣诞假期自发前往阿尔卑斯山滑雪，从此开启了每年一次的集体滑雪传统</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">2019</div>
        <div class="timeline-content">
          <h3>成长：30人的雪山共鸣</h3>
          <p class="timeline-location">📍 法国·Val Thorens</p>
          <p>SnowNavi至今最大规模的滑雪活动，30人大部队畅滑三峡谷"以雪会友"</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">2021</div>
        <div class="timeline-content">
          <h3>转型：从热爱到专业</h3>
          <p class="timeline-location">📍 疫情后的新起点</p>
          <p>核心成员开始系统性学习滑雪教学，陆续考取CASI/BASI国际滑雪教练认证</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">2023</div>
        <div class="timeline-content">
          <h3>新生：官方认证的里程碑</h3>
          <p class="timeline-location">📍 荷兰商会（KVK）</p>
          <p>随着荷兰滑雪爱好者需求增长，SnowNavi正式注册为合法机构获得荷兰官方商业资质</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">2024</div>
        <div class="timeline-content">
          <h3>桥梁：当欧洲遇见国货雪具</h3>
          <p class="timeline-location">📍 中欧滑雪生态圈</p>
          <p>携手Outdoor Master等国产雪具品牌尝试出海荷兰及欧洲市场，以装备赞助形式支持欧洲双板自由式俱乐部FreeGaze</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">2025</div>
        <div class="timeline-content">
          <h3>革新：全欧滑雪的新篇章</h3>
          <p class="timeline-location">📍 荷兰SnowWorld</p>
          <p>与BASI官方合作伙伴SnowboardCoach深度合作，成为欧洲大陆唯一获得授权在室内雪场开展BASI滑雪指导员培训的组织</p>
          <p>独立开发并上线SnowNavi.ski在线雪场地图支持滑雪路线规划帮助小伙伴们探索全山不再迷路</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">未来</div>
        <div class="timeline-content">
          <h3>持续书写</h3>
          <p>从阿尔卑斯山的第一道雪痕，到与滑雪社群的携手合作，SnowNavi始终相信：滑雪的终极意义，在于打破边界，创造连接。</p>
          <p class="timeline-future">⛷️ To be continued...</p>
        </div>
      </div>
    </div>
  </section>

  <footer id="contact">
    <p>Contact: <EMAIL> | Follow our 微信公共号 SnowNavi指雪针 for updates</p>
    <p>&copy; 2025 SnowNavi Sports. All rights reserved.</p>
  </footer>

  <button id="toTop" onclick="window.scrollTo({ top: 0, behavior: 'smooth' })">↑ Top</button>

  <!-- Include the navigation module -->
  <script src="assets/js/navigation.js"></script>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const zhTexts = {
        title: 'SnowNavi 滑雪俱乐部',
        navCourses: '课程',
        navMap: '在线滑雪地图',
        navContact: '联系我们',
        navStory: '我们的故事',
        navMember: '会员',
        heroText: '无论是进阶挑战，还是初学启蒙，我们陪你畅滑到底',
        ctaBtn: '加入 SnowNavi ',
        sectionTitle: '我们的课程',
        courses: ['BASI 指导员课程', '公园自由式课程', '私教课程', '团体课程', '畅滑周'],
        learnMore: '了解更多',
        mapTitle: '在线滑雪地图',
        storyTitle: '🏂 SnowNavi的故事｜从雪友到雪匠',
        storyText: '“Snow”即雪，象征着纯洁与激情；“Navi”意为指引。我们致力于以雪为引，引领伙伴们畅享自由的滑雪之旅。- Since 2021 @ 🇳🇱',
        contactText: '联系方式：<EMAIL> | 请关注微信公共号： SnowNavi指雪针',
        copyright: '保留所有权利.'
      };

      const enTexts = {
        title: 'SnowNavi Snow Club',
        navCourses: 'Courses',
        navMap: 'Interactive Ski Map',
        navContact: 'Contact',
        navStory: 'Our Story',
        navMember: 'Member',
        heroText: "Whether you're leveling up or starting fresh, we ride with you",
        ctaBtn: 'Join SnowNavi',
        sectionTitle: 'Our Courses',
        courses: ['BASI Instructor Course', 'Park Freestyle Lessons', 'Private Lessons', 'Group Lessons', 'Riding Week'],
        learnMore: 'Learn More',
        mapTitle: 'Interactive Ski Map',
        storyTitle: 'Our Story',
        storyText: '“Snow” stands for purity and passion, while “Navi” means guidance. We are committed to leading our partners through a fun ride on snow. — Since 2021 @ 🇳🇱',
        contactText: 'Contact: <EMAIL> | Follow us on 微信公共号： SnowNavi指雪针',
        copyright: 'All rights reserved.'
      };

      const nlTexts = {
        title: 'SnowNavi Snow Club',
        navCourses: 'Cursussen',
        navMap: 'Interactieve Skikaart',
        navContact: 'Contact',
        navStory: 'Ons Verhaal',
        navMember: 'Lid',
        heroText: 'Of je nu verder wilt groeien of net begint, wij gaan samen de piste af',
        ctaBtn: 'Doe mee met SnowNavi',
        sectionTitle: 'Onze Cursussen',
        courses: ['BASI Instructeurs cursus', 'Freestyle Park lessen', 'Privé lessen', 'Groeps lessen', 'Riding Week'],
        learnMore: 'Meer info',
        mapTitle: 'Interactieve Skikaart',
        storyTitle: 'Ons Verhaal',
        storyText: '“Snow” staat voor puurheid en passie, terwijl “Navi” richting betekent. Wij zetten ons in om onze partners mee te nemen op een plezierige rit door de sneeuw. — Sinds 2021 @ 🇳🇱',
        contactText: 'Contact: <EMAIL> | Volg ons op 微信公共号: SnowNavi指雪针',
        copyright: 'Alle rechten voorbehouden.'
      };

      const langSelect = document.getElementById('lang');
      if (!langSelect) return;

      function setLang(lang) {
        const texts = lang === 'zh' ? zhTexts : lang === 'nl' ? nlTexts : enTexts;
        document.getElementById('page-title').textContent = texts.title;
        document.querySelector('.hero h1').textContent = texts.heroText;
        document.querySelector('.hero .cta-btn').textContent = texts.ctaBtn;
        document.querySelector('#courses h2').textContent = texts.sectionTitle;
        document.querySelectorAll('.course-card h3').forEach((el, i) => {
          if (texts.courses[i]) el.textContent = texts.courses[i];
        });
        document.querySelectorAll('.course-card a').forEach(el => {
          el.textContent = texts.learnMore;
        });
        document.querySelector('#map h2').textContent = texts.mapTitle;
        document.querySelector('#story h2').textContent = texts.storyTitle;
        // Hide the old paragraph and show timeline for Chinese
        const oldParagraph = document.querySelector('#story p');
        const timeline = document.querySelector('#story .timeline');
        if (lang === 'zh') {
          if (oldParagraph) oldParagraph.style.display = 'none';
          if (timeline) timeline.style.display = 'block';
        } else {
          if (oldParagraph) {
            oldParagraph.style.display = 'block';
            oldParagraph.textContent = texts.storyText;
          }
          if (timeline) timeline.style.display = 'none';
        }
        document.querySelector('#contact p:first-child').textContent = texts.contactText;
        document.querySelector('#contact p:last-child').innerHTML = `&copy; 2025 ${lang === 'zh' ? 'SnowNavi 滑雪俱乐部' : 'SnowNavi Sports'}. ${texts.copyright}`;

        // Trigger navigation update with the new language
        if (window.navigationManager) {
          window.navigationManager.currentLang = lang;
          window.navigationManager.renderNavigation();
        }
      }

      const savedLang = localStorage.getItem('preferredLang') || (navigator.language.startsWith('zh') ? 'zh' : navigator.language.startsWith('nl') ? 'nl' : 'en');
      langSelect.value = savedLang;
      setLang(savedLang);

      // Ensure navigation bar language is synchronized
      if (window.navigationManager) {
        window.navigationManager.currentLang = savedLang;
        window.navigationManager.renderNavigation();
      }

      langSelect.addEventListener('change', e => {
        const newLang = e.target.value;
        localStorage.setItem('preferredLang', newLang);
        setLang(newLang);
      });

      // Return to Top logic
      const toTop = document.getElementById('toTop');
      const hero = document.getElementById('hero');

      window.addEventListener('scroll', function () {
        const heroBottom = hero.getBoundingClientRect().bottom;
        toTop.style.display = heroBottom < 0 ? 'block' : 'none';
      });
    });

    // URL转换逻辑：若包含 ?loc= 则重定向到新格式
    (function () {
      const currentUrl = new URL(window.location.href);
      const locParams = currentUrl.searchParams.getAll('loc');

      if (locParams.length > 0) {
        // 将纬度,经度 => 经度,纬度
        const reversedCoords = locParams.map(coord => {
          const [lat, lng] = coord.split(',');
          return `${lng},${lat}`;
        });

        const joinedCoords = reversedCoords.join(';');
        const newUrl = `https://snownavi.ski/preview/?coords=${joinedCoords}&resortKey=3valley`;

        // 重定向到新URL
        window.location.replace(newUrl);
      }
    })();

    document.getElementById('menu-toggle').addEventListener('click', function () {
      document.getElementById('nav-links').classList.toggle('open');
    });
  </script>
</body>
</html>
